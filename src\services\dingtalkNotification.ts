/**
 * 钉钉消息通知服务
 * 专门处理各种业务场景的钉钉消息通知
 */

import type {
  MessageRequest,
  NotificationTemplate,
  ProjectCreatedNotificationData,
  WeeklyBudgetExceededNotificationData
} from '../types/dingtalk.js';
import { db } from './database.js';
import { DingTalkService } from './dingtalk.js';
import { UserSyncService } from './userSync.js';

export class DingTalkNotificationService {
  private dingTalkService: DingTalkService;
  private userSyncService: UserSyncService;

  constructor() {
    this.dingTalkService = new DingTalkService();
    this.userSyncService = new UserSyncService(db, this.dingTalkService);
  }

  /**
   * 发送项目创建通知给执行PM
   */
  async sendProjectCreatedNotification(
    executorPMUserId: string,
    data: ProjectCreatedNotificationData
  ): Promise<boolean> {
    try {
      console.log(`发送项目创建通知给执行PM: ${executorPMUserId}`);

      const template = this.getProjectCreatedTemplate(data);
      
      const messageRequest: MessageRequest = {
        userIds: [executorPMUserId],
        title: template.title,
        content: template.content,
        messageType: template.messageType as any
      };

      const result = await this.dingTalkService.sendWorkNotification(messageRequest);
      
      if (result) {
        console.log(`项目创建通知发送成功: ${data.projectName}`);
      } else {
        console.error(`项目创建通知发送失败: ${data.projectName}`);
      }

      return result;
    } catch (error) {
      console.error('发送项目创建通知失败:', error);
      return false;
    }
  }

  /**
   * 发送周预算超额通知给项目创建人
   */
  async sendWeeklyBudgetExceededNotification(
    creatorUserId: string,
    data: WeeklyBudgetExceededNotificationData
  ): Promise<boolean> {
    try {
      if (!creatorUserId) {
        console.warn('无法发送周预算超额通知，因为没有找到项目创建人');
        return false;
      }

      console.log(`发送周预算超额通知给创建人: ${creatorUserId}`);

      const template = this.getWeeklyBudgetExceededTemplate(data);
      
      const messageRequest: MessageRequest = {
        userIds: [creatorUserId],
        title: template.title,
        content: template.content,
        messageType: template.messageType as any
      };

      const result = await this.dingTalkService.sendWorkNotification(messageRequest);
      
      if (result) {
        console.log(`周预算超额通知发送成功: ${data.projectName} - ${data.weeklyBudgetTitle}`);
      } else {
        console.error(`周预算超额通知发送失败: ${data.projectName} - ${data.weeklyBudgetTitle}`);
      }

      return result;
    } catch (error) {
      console.error('发送周预算超额通知失败:', error);
      return false;
    }
  }

  /**
   * 批量发送通知
   */
  async sendBatchNotification(
    userIds: string[],
    template: NotificationTemplate
  ): Promise<boolean> {
    try {
      console.log(`批量发送通知给 ${userIds.length} 个用户`);

      const messageRequest: MessageRequest = {
        userIds,
        title: template.title,
        content: template.content,
        messageType: template.messageType as any
      };

      const result = await this.dingTalkService.sendWorkNotification(messageRequest);
      
      if (result) {
        console.log(`批量通知发送成功，接收人数: ${userIds.length}`);
      } else {
        console.error(`批量通知发送失败，接收人数: ${userIds.length}`);
      }

      return result;
    } catch (error) {
      console.error('批量发送通知失败:', error);
      return false;
    }
  }

  /**
   * 获取项目创建通知模板
   */
  private getProjectCreatedTemplate(data: ProjectCreatedNotificationData): NotificationTemplate {
    const title = `新项目分配通知 - ${data.projectName}`;
    
    const content = `📋 **新项目分配通知**

**项目名称：** ${data.projectName}
**所属品牌：** ${data.brandName}
**项目预算：** ¥${data.budget.toLocaleString()}
**项目周期：** ${data.startDate} 至 ${data.endDate}
**项目创建人：** ${data.creatorName}

您已被指定为该项目的执行PM，请及时关注项目进展并做好相关准备工作。

如有疑问，请联系项目创建人。`;

    return {
      title,
      content,
      messageType: 'text'
    };
  }

  /**
   * 获取周预算超额通知模板
   */
  private getWeeklyBudgetExceededTemplate(data: WeeklyBudgetExceededNotificationData): NotificationTemplate {
    const title = `周预算超额预警 - ${data.projectName}`;
    
    const content = `⚠️ **周预算超额预警**

**项目名称：** ${data.projectName}
**所属品牌：** ${data.brandName}
**周预算标题：** ${data.weeklyBudgetTitle}
**合同金额：** ¥${data.contractAmount.toLocaleString()}
**项目总成本：** ¥${data.projectCost.toLocaleString()}
**超额比例：** ${data.exceedPercentage.toFixed(1)}%

该周预算金额已超过项目配置成本的10%，请注意控制预算支出，确保项目成本在合理范围内。

如需调整预算，请及时与相关人员沟通确认。`;

    return {
      title,
      content,
      messageType: 'text'
    };
  }

  /**
   * 获取用户信息（用于通知中显示用户名）
   */
  async getUserName(userId: string): Promise<string> {
    try {
      const userInfo = await this.userSyncService.getUserInfo(userId);
      return userInfo?.name || userId;
    } catch (error) {
      console.error(`获取用户信息失败: ${userId}`, error);
      return userId;
    }
  }

  /**
   * 验证用户是否存在
   */
  async validateUser(userId: string): Promise<boolean> {
    try {
      const userInfo = await this.userSyncService.getUserInfo(userId);
      return userInfo !== null;
    } catch (error) {
      console.error(`验证用户失败: ${userId}`, error);
      return false;
    }
  }

  /**
   * 发送自定义通知
   */
  async sendCustomNotification(
    userIds: string[],
    title: string,
    content: string,
    messageType: 'text' | 'markdown' = 'text'
  ): Promise<boolean> {
    try {
      const template: NotificationTemplate = {
        title,
        content,
        messageType
      };

      return await this.sendBatchNotification(userIds, template);
    } catch (error) {
      console.error('发送自定义通知失败:', error);
      return false;
    }
  }
}

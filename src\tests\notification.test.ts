/**
 * 钉钉消息通知功能测试
 * 测试各种场景下的钉钉消息通知功能，确保消息发送正常且内容准确
 */

import { DingTalkNotificationService } from '../services/dingtalkNotification.js';
import { ProjectService } from '../services/project.js';
import type {
    ProjectCreatedNotificationData,
    WeeklyBudgetExceededNotificationData
} from '../types/dingtalk.js';
import type { CreateProjectRequest, CreateWeeklyBudgetRequest } from '../types/project.js';

// 模拟测试数据
const mockProjectData: CreateProjectRequest = {
  documentType: 'project_initiation' as any,
  brandId: 'test-brand-001',
  projectName: '测试项目 - 钉钉通知验证',
  period: {
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-03-31')
  },
  budget: {
    planningBudget: 100000,
    influencerBudget: 40000,
    adBudget: 30000,
    otherBudget: 10000
  },
  cost: {
    influencerCost: 35000,
    adCost: 25000,
    otherCost: 8000,
    intermediaryCost: 5000,
    estimatedInfluencerRebate: 2000
  },
  executorPM: 'test-pm-001',
  contentMediaIds: ['test-media-001', 'test-media-002'],
  contractType: 'single' as any,
  settlementRules: '测试结算规则',
  kpi: '测试KPI指标'
};

const mockWeeklyBudgetData: CreateWeeklyBudgetRequest = {
  title: '测试周预算 - 超额验证',
  weekStartDate: new Date('2024-01-01'),
  weekEndDate: new Date('2024-01-07'),
  serviceType: 'influencer' as any,
  serviceContent: '达人合作服务',
  contractAmount: 15000, // 超过项目总成本(68000)的10%
  taxRate: 'general' as any
};

/**
 * 测试项目创建通知功能
 */
export async function testProjectCreatedNotification(): Promise<boolean> {
  console.log('\n🧪 测试项目创建通知功能...');
  
  try {
    const notificationService = new DingTalkNotificationService();
    
    // 构建测试通知数据
    const notificationData: ProjectCreatedNotificationData = {
      projectName: mockProjectData.projectName,
      brandName: '测试品牌',
      executorPMName: '测试PM',
      budget: mockProjectData.budget.planningBudget,
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      creatorName: '测试创建人'
    };
    
    // 发送测试通知
    const success = await notificationService.sendProjectCreatedNotification(
      'test-pm-001',
      notificationData
    );
    
    if (success) {
      console.log('✅ 项目创建通知测试成功');
      return true;
    } else {
      console.log('❌ 项目创建通知测试失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 项目创建通知测试出错:', error);
    return false;
  }
}

/**
 * 测试周预算超额通知功能
 */
export async function testWeeklyBudgetExceededNotification(): Promise<boolean> {
  console.log('\n🧪 测试周预算超额通知功能...');
  
  try {
    const notificationService = new DingTalkNotificationService();
    
    // 构建测试通知数据
    const notificationData: WeeklyBudgetExceededNotificationData = {
      projectName: mockProjectData.projectName,
      brandName: '测试品牌',
      weeklyBudgetTitle: mockWeeklyBudgetData.title,
      contractAmount: mockWeeklyBudgetData.contractAmount,
      projectCost: 68000, // 项目总成本
      exceedPercentage: 22.1, // 超额比例
      creatorName: '测试创建人'
    };
    
    // 发送测试通知
    const success = await notificationService.sendWeeklyBudgetExceededNotification(
      'test-creator-001',
      notificationData
    );
    
    if (success) {
      console.log('✅ 周预算超额通知测试成功');
      return true;
    } else {
      console.log('❌ 周预算超额通知测试失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 周预算超额通知测试出错:', error);
    return false;
  }
}

/**
 * 测试项目服务集成的通知功能
 */
export async function testProjectServiceIntegration(): Promise<boolean> {
  console.log('\n🧪 测试项目服务集成的通知功能...');
  
  try {
    const projectService = new ProjectService();
    
    // 测试项目创建（会触发通知）
    console.log('📝 创建测试项目...');
    const project = await projectService.createProject(
      mockProjectData,
      'test-creator-001'
    );
    
    console.log(`✅ 项目创建成功: ${project.id}`);
    
    // 测试周预算创建（会触发成本检查和通知）
    console.log('📝 创建测试周预算...');
    const weeklyBudget = await projectService.createWeeklyBudget(
      project.id,
      mockWeeklyBudgetData,
      'test-creator-001'
    );
    
    console.log(`✅ 周预算创建成功: ${weeklyBudget.id}`);
    
    // 等待异步通知处理完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('✅ 项目服务集成测试完成');
    return true;
  } catch (error) {
    console.error('❌ 项目服务集成测试出错:', error);
    return false;
  }
}

/**
 * 测试用户验证功能
 */
export async function testUserValidation(): Promise<boolean> {
  console.log('\n🧪 测试用户验证功能...');
  
  try {
    const notificationService = new DingTalkNotificationService();
    
    // 测试有效用户
    const validUser = await notificationService.validateUser('test-pm-001');
    console.log(`用户验证结果 (test-pm-001): ${validUser}`);
    
    // 测试无效用户
    const invalidUser = await notificationService.validateUser('invalid-user-999');
    console.log(`用户验证结果 (invalid-user-999): ${invalidUser}`);
    
    console.log('✅ 用户验证测试完成');
    return true;
  } catch (error) {
    console.error('❌ 用户验证测试出错:', error);
    return false;
  }
}

/**
 * 运行所有测试
 */
export async function runAllNotificationTests(): Promise<void> {
  console.log('🚀 开始钉钉消息通知功能测试...\n');
  
  const results = {
    projectCreatedNotification: false,
    weeklyBudgetExceededNotification: false,
    projectServiceIntegration: false,
    userValidation: false
  };
  
  // 运行各项测试
  results.projectCreatedNotification = await testProjectCreatedNotification();
  results.weeklyBudgetExceededNotification = await testWeeklyBudgetExceededNotification();
  results.projectServiceIntegration = await testProjectServiceIntegration();
  results.userValidation = await testUserValidation();
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('================');
  console.log(`项目创建通知: ${results.projectCreatedNotification ? '✅ 通过' : '❌ 失败'}`);
  console.log(`周预算超额通知: ${results.weeklyBudgetExceededNotification ? '✅ 通过' : '❌ 失败'}`);
  console.log(`项目服务集成: ${results.projectServiceIntegration ? '✅ 通过' : '❌ 失败'}`);
  console.log(`用户验证功能: ${results.userValidation ? '✅ 通过' : '❌ 失败'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n总体结果: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！钉钉消息通知功能正常工作。');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能。');
  }
}

// 如果直接运行此文件，执行所有测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllNotificationTests().catch(console.error);
}

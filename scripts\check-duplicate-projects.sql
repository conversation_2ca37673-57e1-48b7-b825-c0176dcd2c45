-- 查找重复的项目名称
SELECT 
    "projectName",
    COUNT(*) as count,
    STRING_AGG(id, ', ') as project_ids,
    STRING_AGG("createdAt"::text, ', ') as created_dates
FROM projects 
GROUP BY "projectName" 
HAVING COUNT(*) > 1
ORDER BY count DESC, "projectName";

-- 查看具体的重复项目详情
SELECT 
    id,
    "projectName",
    "brandId",
    "createdAt",
    "createdBy"
FROM projects 
WHERE "projectName" IN (
    SELECT "projectName" 
    FROM projects 
    GROUP BY "projectName" 
    HAVING COUNT(*) > 1
)
ORDER BY "projectName", "createdAt";

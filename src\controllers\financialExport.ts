import { FastifyReply, FastifyRequest } from 'fastify';
import fs, { writeFile } from 'fs';
import { z } from 'zod';
import { FinancialExportParams, FinancialExportService } from '../services/financialExport.js';

// 验证模式
const financialExportQuerySchema = z.object({
  year: z.string().transform(str => parseInt(str, 10)).refine(year => year >= 2020 && year <= 2030, '年份必须在2020-2030之间'),
  brandIds: z.string().optional().transform(str => str ? str.split(',') : undefined),
  includeCompleted: z.string().optional().transform(str => str === 'true'),
  includeCancelled: z.string().optional().transform(str => str === 'true')
});

export class FinancialExportController {
  private financialExportService: FinancialExportService;

  constructor() {
    this.financialExportService = new FinancialExportService();
  }

  /**
   * 导出财务报表
   */
  async exportFinancialReport(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 验证查询参数
      const validatedQuery = financialExportQuerySchema.parse(request.query);
      
      const params: FinancialExportParams = {
        year: validatedQuery.year,
        brandIds: validatedQuery.brandIds,
        includeCompleted: validatedQuery.includeCompleted ?? true,
        includeCancelled: validatedQuery.includeCancelled ?? false
      };

      console.log('📊 开始导出财务报表:', params);

      // 生成Excel文件
      const buffer = await this.financialExportService.exportFinancialReport(params);

      // 设置响应头
      const filename = `财务报表_${params.year}年_${new Date().toISOString().split('T')[0]}.xlsx`;
      
      reply.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      reply.header('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      reply.header('Content-Length', buffer.length);

      console.log(`✅ 财务报表导出成功: ${filename} (${buffer.length} bytes)`);

      return reply.send(buffer);

    } catch (error) {
      console.error('❌ 财务报表导出失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        });
      }

      return reply.status(500).send({
        success: false,
        message: '导出财务报表失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取可导出的年份列表
   */
  async getAvailableYears(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 获取系统中有数据的年份
      const currentYear = new Date().getFullYear();
      const years = [];
      
      // 提供最近5年的选项
      for (let i = 0; i < 5; i++) {
        years.push(currentYear - i);
      }

      return reply.send({
        success: true,
        data: {
          years,
          currentYear,
          defaultYear: currentYear
        },
        message: '获取可导出年份成功'
      });

    } catch (error) {
      console.error('❌ 获取可导出年份失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取可导出年份失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取导出预览信息
   */
  async getExportPreview(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 验证查询参数
      const validatedQuery = financialExportQuerySchema.parse(request.query);
      
      const params: FinancialExportParams = {
        year: validatedQuery.year,
        brandIds: validatedQuery.brandIds,
        includeCompleted: validatedQuery.includeCompleted ?? true,
        includeCancelled: validatedQuery.includeCancelled ?? false
      };

      console.log('👀 获取导出预览信息:', params);

      // 这里可以添加预览逻辑，比如统计数据量
      const preview = {
        year: params.year,
        estimatedSheets: 3, // 项目汇总表 + 品牌汇总表 + 各品牌详情表
        filters: {
          brandIds: params.brandIds,
          includeCompleted: params.includeCompleted,
          includeCancelled: params.includeCancelled
        },
        estimatedSize: '预计 1-5MB',
        estimatedTime: '预计 10-30秒',
        sheets: [
          {
            name: '项目汇总表',
            description: '包含所有项目的详细财务信息',
            columns: 32
          },
          {
            name: '品牌汇总表',
            description: '各品牌的财务汇总数据',
            columns: 13
          },
          {
            name: '各品牌详情表',
            description: '每个品牌单独的项目明细表',
            columns: 17
          }
        ]
      };

      return reply.send({
        success: true,
        data: preview,
        message: '获取导出预览成功'
      });

    } catch (error) {
      console.error('❌ 获取导出预览失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取导出预览失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取导出历史记录
   */
  async getExportHistory(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 这里可以从数据库获取导出历史记录
      // 暂时返回模拟数据
      const history = [
        {
          id: '1',
          filename: '财务报表_2024年_2024-06-19.xlsx',
          year: 2024,
          exportedAt: new Date().toISOString(),
          exportedBy: 'current-user',
          fileSize: '2.3MB',
          status: 'completed'
        }
      ];

      return reply.send({
        success: true,
        data: {
          history,
          total: history.length
        },
        message: '获取导出历史成功'
      });

    } catch (error) {
      console.error('❌ 获取导出历史失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取导出历史失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  async getExportDownloadUrl(request: FastifyRequest, reply: FastifyReply) {
    try {
      const currentYear = new Date().getFullYear();
      // You may want to validate query parameters here as in other methods
      const validatedQuery = financialExportQuerySchema.parse(request.query);

      const params: FinancialExportParams = {
        year: currentYear,
        brandIds: validatedQuery.brandIds,
        includeCompleted: validatedQuery.includeCompleted ?? true,
        includeCancelled: validatedQuery.includeCancelled ?? false
      };

      console.log('📊 开始导出财务报表:', params);

      // 生成Excel文件
      const buffer = await this.financialExportService.exportFinancialReport(params);
      const id = '123456';
      const filename = `财务报表_${params.year}年_${Date.now()}.xlsx`;
      const filepath = `public/financial/${filename}`;
      if (!fs.existsSync('public')) {
        fs.mkdirSync('public');
      }
      if (!fs.existsSync('public/financial')) {
        fs.mkdirSync('public/financial');
      }
      await writeFile(filepath, buffer, {}, (err) => {
        if (err) throw err;
      });
      console.log(`✅ 财务报表导出成功: ${filepath} (${buffer.length} bytes)`);
      const url = `financial/${filename}`;

      return reply.send({
        success: true,
        data: {
          url,
          filename
        },
        message: '获取导出下载链接成功'
      });

    } catch (error) {
      console.error('❌ 获取导出下载链接失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取导出下载链接失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }


}

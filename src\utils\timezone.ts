import { env } from '../config/env.js';

/**
 * 时区工具类
 */
export class TimezoneUtils {
  private static readonly timezone = env.TIMEZONE;

  /**
   * 获取当前时区
   */
  static getTimezone(): string {
    return this.timezone;
  }

  /**
   * 获取当前时间（带时区）
   */
  static now(): Date {
    return new Date();
  }

  /**
   * 格式化日期为本地时区字符串
   */
  static formatToLocal(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleString('zh-CN', {
      timeZone: this.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  /**
   * 格式化日期为ISO字符串（带时区偏移）
   */
  static formatToISO(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toISOString();
  }

  /**
   * 格式化日期为本地日期字符串（YYYY-MM-DD）
   */
  static formatToLocalDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('zh-CN', {
      timeZone: this.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
  }

  /**
   * 格式化日期为本地时间字符串（HH:mm:ss）
   */
  static formatToLocalTime(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleTimeString('zh-CN', {
      timeZone: this.timezone,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  /**
   * 获取今天的开始时间（00:00:00）
   */
  static getTodayStart(): Date {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    return today;
  }

  /**
   * 获取今天的结束时间（23:59:59.999）
   */
  static getTodayEnd(): Date {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return today;
  }

  /**
   * 获取本周的开始时间（周一 00:00:00）
   */
  static getWeekStart(): Date {
    const now = new Date();
    const day = now.getDay();
    const diff = now.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    const monday = new Date(now.getFullYear(), now.getMonth(), diff);
    return monday;
  }

  /**
   * 获取本周的结束时间（周日 23:59:59.999）
   */
  static getWeekEnd(): Date {
    const weekStart = this.getWeekStart();
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    return weekEnd;
  }

  /**
   * 获取本月的开始时间（1号 00:00:00）
   */
  static getMonthStart(year?: number, month?: number): Date {
    const now = new Date();
    const targetYear = year ?? now.getFullYear();
    const targetMonth = month ?? now.getMonth();
    return new Date(targetYear, targetMonth, 1);
  }

  /**
   * 获取本月的结束时间（最后一天 23:59:59.999）
   */
  static getMonthEnd(year?: number, month?: number): Date {
    const now = new Date();
    const targetYear = year ?? now.getFullYear();
    const targetMonth = month ?? now.getMonth();
    return new Date(targetYear, targetMonth + 1, 0, 23, 59, 59, 999);
  }

  /**
   * 获取本年的开始时间（1月1日 00:00:00）
   */
  static getYearStart(year?: number): Date {
    const targetYear = year ?? new Date().getFullYear();
    return new Date(targetYear, 0, 1);
  }

  /**
   * 获取本年的结束时间（12月31日 23:59:59.999）
   */
  static getYearEnd(year?: number): Date {
    const targetYear = year ?? new Date().getFullYear();
    return new Date(targetYear, 11, 31, 23, 59, 59, 999);
  }

  /**
   * 解析日期字符串为Date对象
   */
  static parseDate(dateString: string): Date {
    return new Date(dateString);
  }

  /**
   * 计算两个日期之间的天数差
   */
  static daysBetween(date1: Date | string, date2: Date | string): number {
    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * 添加天数到日期
   */
  static addDays(date: Date | string, days: number): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setDate(d.getDate() + days);
    return d;
  }

  /**
   * 添加小时到日期
   */
  static addHours(date: Date | string, hours: number): Date {
    const d = typeof date === 'string' ? new Date(date) : new Date(date);
    d.setHours(d.getHours() + hours);
    return d;
  }

  /**
   * 检查日期是否为今天
   */
  static isToday(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return d.toDateString() === today.toDateString();
  }

  /**
   * 检查日期是否为本周
   */
  static isThisWeek(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    const weekStart = this.getWeekStart();
    const weekEnd = this.getWeekEnd();
    return d >= weekStart && d <= weekEnd;
  }

  /**
   * 检查日期是否为本月
   */
  static isThisMonth(date: Date | string): boolean {
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    return d.getFullYear() === now.getFullYear() && d.getMonth() === now.getMonth();
  }

  /**
   * 获取时区偏移信息
   */
  static getTimezoneOffset(): {
    offset: number;
    offsetString: string;
    name: string;
  } {
    const now = new Date();
    const offset = -now.getTimezoneOffset(); // 转换为正确的偏移值
    const hours = Math.floor(Math.abs(offset) / 60);
    const minutes = Math.abs(offset) % 60;
    const sign = offset >= 0 ? '+' : '-';
    const offsetString = `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    
    return {
      offset,
      offsetString,
      name: this.timezone
    };
  }

  /**
   * 格式化持续时间（毫秒转为可读格式）
   */
  static formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 获取指定日期所在月份的最后一天
   */
  static getLastDayOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0);
  }

  /**
   * 获取下个月的最后一天
   */
  static getLastDayOfNextMonth(date: Date = new Date()): Date {
    return new Date(date.getFullYear(), date.getMonth() + 2, 0);
  }

  /**
   * 计算日期差（天数）
   */
  static getDaysDifference(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * 获取当月剩余天数
   */
  static getRemainingDaysInMonth(date: Date = new Date()): number {
    const lastDay = this.getLastDayOfMonth(date);
    return lastDay.getDate() - date.getDate();
  }
}

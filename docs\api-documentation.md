# 项目管理系统 API 接口文档

## 📋 概述

本文档描述了项目管理系统的所有API接口，包括项目管理、品牌管理、文件上传和统计分析等功能。

**基础信息**：
- **Base URL**: `http://localhost:3000/api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 🏷️ 品牌管理 API

### 1. 获取品牌列表

**接口地址**: `GET /brands`

**请求参数**:
```typescript
interface BrandQueryParams {
  page?: number;          // 页码，默认1
  pageSize?: number;      // 每页数量，默认50
  status?: 'active' | 'inactive';  // 品牌状态
  keyword?: string;       // 搜索关键字
  sortBy?: 'name' | 'createdAt' | 'updatedAt';  // 排序字段
  sortOrder?: 'asc' | 'desc';  // 排序方向
}
```

**请求示例**:
```bash
GET /api/brands?page=1&pageSize=20&status=active&keyword=可口可乐
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "brands": [
      {
        "id": "brand-001",
        "name": "可口可乐",
        "description": "全球知名饮料品牌",
        "logo": "https://example.com/logo.png",
        "status": "active",
        "createdAt": "2024-01-15T08:00:00.000Z",
        "updatedAt": "2024-01-15T08:00:00.000Z",
        "createdBy": "admin"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": "获取品牌列表成功"
}
```

### 2. 获取单个品牌

**接口地址**: `GET /brands/{id}`

**路径参数**:
- `id`: 品牌ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "brand-001",
    "name": "可口可乐",
    "description": "全球知名饮料品牌",
    "logo": "https://example.com/logo.png",
    "status": "active",
    "createdAt": "2024-01-15T08:00:00.000Z",
    "updatedAt": "2024-01-15T08:00:00.000Z",
    "createdBy": "admin"
  },
  "message": "获取品牌成功"
}
```

### 3. 创建品牌

**接口地址**: `POST /brands`

**请求体**:
```typescript
interface CreateBrandRequest {
  name: string;           // 品牌名称（必填）
  description?: string;   // 品牌描述
  logo?: string;         // 品牌Logo URL
}
```

**请求示例**:
```json
{
  "name": "新品牌",
  "description": "这是一个新品牌",
  "logo": "https://example.com/new-logo.png"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "brand-002",
    "name": "新品牌",
    "description": "这是一个新品牌",
    "logo": "https://example.com/new-logo.png",
    "status": "active",
    "createdAt": "2024-01-15T09:00:00.000Z",
    "updatedAt": "2024-01-15T09:00:00.000Z",
    "createdBy": "current-user"
  },
  "message": "品牌创建成功"
}
```

### 4. 更新品牌

**接口地址**: `PUT /brands`

**请求体**:
```typescript
interface UpdateBrandRequest {
  id: string;             // 品牌ID（必填）
  name?: string;          // 品牌名称
  description?: string;   // 品牌描述
  logo?: string;         // 品牌Logo URL
  status?: 'active' | 'inactive';  // 品牌状态
}
```

**请求示例**:
```json
{
  "id": "brand-002",
  "name": "更新后的品牌名称",
  "status": "active"
}
```

### 5. 删除品牌

**接口地址**: `DELETE /brands/{id}`

**路径参数**:
- `id`: 品牌ID

**响应示例**:
```json
{
  "success": true,
  "message": "品牌删除成功"
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "无法删除品牌，存在关联的项目"
}
```

## 📊 项目管理 API

### 1. 获取项目列表

**接口地址**: `GET /projects`

**请求参数**:
```typescript
interface ProjectQueryParams {
  page?: number;          // 页码，默认1
  pageSize?: number;      // 每页数量，默认20
  documentType?: 'project_initiation';  // 单据类型
  brandId?: string;       // 品牌ID
  contractType?: 'annual_frame' | 'quarterly_frame' | 'single' | 'po_order' | 'jing_task';
  executorPM?: string;    // 执行PM用户ID
  status?: 'draft' | 'active' | 'completed' | 'cancelled';  // 项目状态
  keyword?: string;       // 项目名称关键字
  startDate?: string;     // 开始日期 (YYYY-MM-DD)
  endDate?: string;       // 结束日期 (YYYY-MM-DD)
  sortBy?: 'createdAt' | 'updatedAt' | 'projectName' | 'profit';  // 排序字段
  sortOrder?: 'asc' | 'desc';  // 排序方向
}
```

**请求示例**:
```bash
GET /api/projects?page=1&pageSize=20&brandId=brand-001&status=active
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "projects": [
      {
        "id": "project-001",
        "documentType": "project_initiation",
        "brandId": "brand-001",
        "brand": {
          "id": "brand-001",
          "name": "可口可乐",
          "status": "active"
        },
        "projectName": "春节营销活动",
        "period": {
          "startDate": "2024-02-01T00:00:00.000Z",
          "endDate": "2024-02-29T00:00:00.000Z"
        },
        "budget": {
          "planningBudget": 1000000.00,
          "influencerBudget": 400000.00,
          "adBudget": 300000.00,
          "otherBudget": 100000.00
        },
        "cost": {
          "influencerCost": 350000.00,
          "adCost": 280000.00,
          "otherCost": 80000.00,
          "estimatedInfluencerRebate": 20000.00
        },
        "profit": {
          "profit": 310000.00,
          "grossMargin": 31.00
        },
        "executorPM": "user-001",
        "contentMediaIds": ["user-002", "user-003"],
        "contractType": "annual_frame",
        "settlementRules": "<p>按月结算，每月25日前提交结算单</p>",
        "kpi": "<p><strong>目标指标：</strong></p><ul><li>曝光量：1000万+</li></ul>",
        "attachments": [],
        "status": "active",
        "createdAt": "2024-01-15T08:00:00.000Z",
        "updatedAt": "2024-01-15T08:00:00.000Z",
        "createdBy": "admin",
        "updatedBy": "admin"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": "获取项目列表成功"
}
```

### 2. 获取单个项目

**接口地址**: `GET /projects/{id}`

**路径参数**:
- `id`: 项目ID

**响应示例**: 同项目列表中的单个项目对象

### 3. 创建项目

**接口地址**: `POST /projects`

**请求体**:
```typescript
interface CreateProjectRequest {
  documentType: 'project_initiation';  // 单据类型
  brandId: string;                     // 品牌ID（必填）
  projectName: string;                 // 项目名称（必填）
  period: {                           // 项目执行周期（必填）
    startDate: string;                // 开始日期 (YYYY-MM-DD)
    endDate: string;                  // 结束日期 (YYYY-MM-DD)
  };
  budget: {                           // 预算信息（必填）
    planningBudget: number;           // 项目规划预算
    influencerBudget: number;         // 达人预算
    adBudget: number;                 // 投流预算
    otherBudget: number;              // 其他预算
  };
  cost: {                             // 成本信息（必填）
    influencerCost: number;           // 达人成本
    adCost: number;                   // 投流成本
    otherCost: number;                // 其他成本
    intermediaryCost?: number;        // 居间费（可选，默认为0）
    estimatedInfluencerRebate: number; // 预估达人返点
  };
  executorPM: string;                 // 执行PM用户ID（必填）
  contentMediaIds: string[];          // 内容媒介用户ID列表（必填）
  contractType: 'annual_frame' | 'quarterly_frame' | 'single' | 'po_order' | 'jing_task';  // 合同类型（必填）
  settlementRules: string;            // 项目结算规则（必填，支持富文本）
  kpi: string;                        // KPI（必填，支持富文本）
}
```

**请求示例**:
```json
{
  "documentType": "project_initiation",
  "brandId": "brand-001",
  "projectName": "春节营销活动",
  "period": {
    "startDate": "2024-02-01",
    "endDate": "2024-02-29"
  },
  "budget": {
    "planningBudget": 1000000.00,
    "influencerBudget": 400000.00,
    "adBudget": 300000.00,
    "otherBudget": 100000.00
  },
  "cost": {
    "influencerCost": 350000.00,
    "adCost": 280000.00,
    "otherCost": 80000.00,
    "intermediaryCost": 15000.00,
    "estimatedInfluencerRebate": 20000.00
  },
  "executorPM": "user-001",
  "contentMediaIds": ["user-002", "user-003"],
  "contractType": "annual_frame",
  "settlementRules": "<p>按月结算，每月25日前提交结算单</p>",
  "kpi": "<p><strong>目标指标：</strong></p><ul><li>曝光量：1000万+</li></ul>"
}
```

**响应示例**: 同获取单个项目的响应格式

### 4. 更新项目

**接口地址**: `PUT /projects`

**请求体**: 同创建项目，但所有字段都是可选的，需要包含项目ID

```typescript
interface UpdateProjectRequest {
  id: string;  // 项目ID（必填）
  // 其他字段都是可选的，同CreateProjectRequest
}
```

### 5. 删除项目

**接口地址**: `DELETE /projects/{id}`

**路径参数**:
- `id`: 项目ID

**响应示例**:
```json
{
  "success": true,
  "message": "项目删除成功"
}
```

## 📈 统计分析 API

### 获取项目统计

**接口地址**: `GET /projects/stats`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalProjects": 10,              // 总项目数
    "activeProjects": 5,              // 活跃项目数
    "completedProjects": 3,           // 已完成项目数
    "totalBudget": 5000000.00,        // 总预算
    "totalProfit": 1500000.00,        // 总利润
    "averageGrossMargin": 30.00,      // 平均毛利率
    "projectsByBrand": [              // 按品牌统计
      {
        "brandId": "brand-001",
        "brandName": "可口可乐",
        "count": 5,
        "totalBudget": 3000000.00
      }
    ],
    "projectsByContractType": [       // 按合同类型统计
      {
        "contractType": "annual_frame",
        "count": 3,
        "totalBudget": 2000000.00
      }
    ]
  },
  "message": "获取项目统计成功"
}
```

## 📎 文件上传 API

### 上传文件

**接口地址**: `POST /upload`

**请求类型**: `multipart/form-data`

**请求参数**:
- `file`: 文件对象（必填）

**请求示例** (JavaScript):
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('/api/upload', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "file-001",
    "filename": "contract_20240115.pdf",
    "originalName": "合同文件.pdf",
    "size": 1024000,
    "mimeType": "application/pdf",
    "url": "/uploads/contract_20240115.pdf"
  },
  "message": "文件上传成功"
}
```

## 🔧 数据类型定义

### 枚举类型

```typescript
// 单据类型
enum DocumentType {
  PROJECT_INITIATION = 'project_initiation'  // 项目立项表
}

// 合同类型
enum ContractType {
  ANNUAL_FRAME = 'annual_frame',      // 年框
  QUARTERLY_FRAME = 'quarterly_frame', // 季框
  SINGLE = 'single',                  // 单次
  PO_ORDER = 'po_order',             // PO单
  JING_TASK = 'jing_task'            // 京任务
}

// 项目状态
enum ProjectStatus {
  DRAFT = 'draft',         // 草稿
  ACTIVE = 'active',       // 进行中
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled'  // 已取消
}

// 品牌状态
enum BrandStatus {
  ACTIVE = 'active',       // 启用
  INACTIVE = 'inactive'    // 禁用
}
```

### 核心数据结构

```typescript
// 品牌
interface Brand {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  status: BrandStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

// 项目
interface Project {
  id: string;
  documentType: DocumentType;
  brandId: string;
  brand?: Brand;
  projectName: string;
  period: {
    startDate: string;
    endDate: string;
  };
  budget: {
    planningBudget: number;
    influencerBudget: number;
    adBudget: number;
    otherBudget: number;
  };
  cost: {
    influencerCost: number;
    adCost: number;
    otherCost: number;
    estimatedInfluencerRebate: number;
  };
  profit: {
    profit: number;        // 项目利润 = 规划预算 - 总成本 + 返点
    grossMargin: number;   // 毛利率 = 利润 / 规划预算 * 100
  };
  executorPM: string;
  contentMediaIds: string[];
  contractType: ContractType;
  settlementRules: string;
  kpi: string;
  attachments: Attachment[];
  status: ProjectStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// 附件
interface Attachment {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  uploadedBy: string;
  uploadedAt: string;
}
```

## ❌ 错误处理

### 统一错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息（可选）"
}
```

### 常见错误码

| HTTP状态码 | 错误码 | 描述 |
|-----------|--------|------|
| 400 | INVALID_REQUEST | 请求参数无效 |
| 401 | UNAUTHORIZED | 未授权访问 |
| 403 | FORBIDDEN | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 409 | CONFLICT | 资源冲突（如重复创建） |
| 422 | VALIDATION_ERROR | 数据验证失败 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

### 错误示例

**验证错误**:
```json
{
  "success": false,
  "message": "数据验证失败",
  "code": "VALIDATION_ERROR",
  "details": {
    "projectName": "项目名称不能为空",
    "budget.planningBudget": "规划预算必须大于0"
  }
}
```

**资源不存在**:
```json
{
  "success": false,
  "message": "项目不存在",
  "code": "NOT_FOUND"
}
```

**业务逻辑错误**:
```json
{
  "success": false,
  "message": "无法删除品牌，存在关联的项目",
  "code": "CONFLICT"
}
```

## 🔐 认证和权限

### 请求头

```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

### 钉钉集成

如果使用钉钉免登录，需要在请求头中包含：

```http
X-DingTalk-Signature: <signature>
X-DingTalk-Timestamp: <timestamp>
X-DingTalk-Nonce: <nonce>
```

## 🌐 前端集成示例

### JavaScript/TypeScript

```typescript
// API客户端类
class ProjectManagementAPI {
  private baseURL = 'http://localhost:3000/api';
  private token?: string;

  constructor(token?: string) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || '请求失败');
    }

    return data.data;
  }

  // 品牌管理
  async getBrands(params?: BrandQueryParams) {
    const query = new URLSearchParams(params as any).toString();
    return this.request<BrandListResponse>(`/brands?${query}`);
  }

  async createBrand(data: CreateBrandRequest) {
    return this.request<Brand>('/brands', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateBrand(data: UpdateBrandRequest) {
    return this.request<Brand>('/brands', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteBrand(id: string) {
    return this.request(`/brands/${id}`, {
      method: 'DELETE',
    });
  }

  // 项目管理
  async getProjects(params?: ProjectQueryParams) {
    const query = new URLSearchParams(params as any).toString();
    return this.request<ProjectListResponse>(`/projects?${query}`);
  }

  async getProject(id: string) {
    return this.request<Project>(`/projects/${id}`);
  }

  async createProject(data: CreateProjectRequest) {
    return this.request<Project>('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(data: UpdateProjectRequest) {
    return this.request<Project>('/projects', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: string) {
    return this.request(`/projects/${id}`, {
      method: 'DELETE',
    });
  }

  // 统计分析
  async getProjectStats() {
    return this.request<ProjectStats>('/projects/stats');
  }

  // 文件上传
  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      headers: this.token ? {
        Authorization: `Bearer ${this.token}`,
      } : {},
      body: formData,
    });

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || '文件上传失败');
    }

    return data.data;
  }
}

// 使用示例
const api = new ProjectManagementAPI('your-access-token');

// 获取项目列表
try {
  const projects = await api.getProjects({
    page: 1,
    pageSize: 20,
    status: 'active'
  });
  console.log('项目列表:', projects);
} catch (error) {
  console.error('获取项目列表失败:', error.message);
}

// 创建项目
try {
  const newProject = await api.createProject({
    documentType: 'project_initiation',
    brandId: 'brand-001',
    projectName: '新项目',
    period: {
      startDate: '2024-02-01',
      endDate: '2024-02-29'
    },
    budget: {
      planningBudget: 100000,
      influencerBudget: 40000,
      adBudget: 30000,
      otherBudget: 10000
    },
    cost: {
      influencerCost: 35000,
      adCost: 28000,
      otherCost: 8000,
      estimatedInfluencerRebate: 2000
    },
    executorPM: 'user-001',
    contentMediaIds: ['user-002'],
    contractType: 'single',
    settlementRules: '按月结算',
    kpi: '目标曝光量1000万'
  });
  console.log('项目创建成功:', newProject);
} catch (error) {
  console.error('项目创建失败:', error.message);
}
```

### React Hook 示例

```typescript
// 自定义Hook
import { useState, useEffect } from 'react';

export function useProjects(params?: ProjectQueryParams) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pageSize: 20,
    totalPages: 0
  });

  const api = new ProjectManagementAPI();

  const fetchProjects = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await api.getProjects(params);
      setProjects(result.projects);
      setPagination({
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: result.totalPages
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [JSON.stringify(params)]);

  return {
    projects,
    loading,
    error,
    pagination,
    refetch: fetchProjects
  };
}

// 组件使用
function ProjectList() {
  const { projects, loading, error, pagination } = useProjects({
    page: 1,
    pageSize: 20,
    status: 'active'
  });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <div>
      {projects.map(project => (
        <div key={project.id}>
          <h3>{project.projectName}</h3>
          <p>品牌: {project.brand?.name}</p>
          <p>预算: ¥{project.budget.planningBudget.toLocaleString()}</p>
          <p>利润: ¥{project.profit.profit.toLocaleString()}</p>
        </div>
      ))}
      <div>
        第 {pagination.page} 页，共 {pagination.totalPages} 页
      </div>
    </div>
  );
}
```

### Vue.js 示例

```typescript
// Composable
import { ref, reactive, computed } from 'vue';

export function useProjectManagement() {
  const api = new ProjectManagementAPI();

  const projects = ref<Project[]>([]);
  const brands = ref<Brand[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const fetchProjects = async (params?: ProjectQueryParams) => {
    try {
      loading.value = true;
      error.value = null;
      const result = await api.getProjects(params);
      projects.value = result.projects;
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const fetchBrands = async () => {
    try {
      const result = await api.getBrands();
      brands.value = result.brands;
    } catch (err) {
      console.error('获取品牌列表失败:', err);
    }
  };

  const createProject = async (data: CreateProjectRequest) => {
    try {
      const newProject = await api.createProject(data);
      projects.value.unshift(newProject);
      return newProject;
    } catch (err) {
      error.value = err.message;
      throw err;
    }
  };

  return {
    projects: readonly(projects),
    brands: readonly(brands),
    loading: readonly(loading),
    error: readonly(error),
    fetchProjects,
    fetchBrands,
    createProject
  };
}
```

## 🧪 API 测试

### 使用 curl 测试

```bash
# 获取项目统计
curl -X GET "http://localhost:3000/api/projects/stats" \
  -H "Content-Type: application/json"

# 获取品牌列表
curl -X GET "http://localhost:3000/api/brands?page=1&pageSize=10" \
  -H "Content-Type: application/json"

# 创建品牌
curl -X POST "http://localhost:3000/api/brands" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试品牌",
    "description": "这是一个测试品牌"
  }'

# 创建项目
curl -X POST "http://localhost:3000/api/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "documentType": "project_initiation",
    "brandId": "brand-001",
    "projectName": "测试项目",
    "period": {
      "startDate": "2024-02-01",
      "endDate": "2024-02-29"
    },
    "budget": {
      "planningBudget": 100000,
      "influencerBudget": 40000,
      "adBudget": 30000,
      "otherBudget": 10000
    },
    "cost": {
      "influencerCost": 35000,
      "adCost": 28000,
      "otherCost": 8000,
      "estimatedInfluencerRebate": 2000
    },
    "executorPM": "user-001",
    "contentMediaIds": ["user-002"],
    "contractType": "single",
    "settlementRules": "按月结算",
    "kpi": "目标曝光量1000万"
  }'
```

### 使用 Postman

1. **导入集合**: 可以创建Postman集合包含所有API
2. **环境变量**: 设置 `{{baseUrl}}` = `http://localhost:3000/api`
3. **认证**: 在Collection级别设置Bearer Token

## 📝 开发建议

### 1. 错误处理
- 始终检查 `success` 字段
- 根据错误码进行不同处理
- 提供用户友好的错误提示

### 2. 数据验证
- 前端进行基础验证
- 后端验证为准
- 显示详细的验证错误信息

### 3. 性能优化
- 使用分页避免大量数据加载
- 实现搜索防抖
- 缓存不经常变化的数据（如品牌列表）

### 4. 用户体验
- 显示加载状态
- 提供操作反馈
- 实现乐观更新

### 5. 类型安全
- 使用TypeScript定义接口
- 运行时类型检查
- API响应数据验证

## 🔗 相关链接

- [项目管理使用指南](project-management-guide.md)
- [数据库设置指南](database-setup-guide.md)
- [完整实现文档](../DINGTALK_IMPLEMENTATION.md)
- [在线API测试](http://localhost:3000/project-management.html)

# 居间费功能实现文档

## 概述

本次更新在项目管理系统中添加了居间费（Intermediary Cost）字段，该字段作为项目成本的一部分，会在计算项目毛利时被扣除，确保财务数据更加准确地反映项目的真实盈利情况。

## 功能特性

### 1. 数据库层面
- **字段名称**: `intermediaryCost`
- **数据类型**: `DECIMAL(15,2)`
- **默认值**: `0`
- **位置**: `projects` 表的成本信息部分

### 2. API接口支持
- **创建项目**: 支持在创建项目时设置居间费
- **更新项目**: 支持通过更新接口修改居间费
- **字段验证**: 确保居间费不能为负数
- **默认值**: 如果请求中未提供居间费，默认为0

### 3. 毛利计算更新
**新的计算公式**:
```
总成本 = 达人成本 + 投流成本 + 其他成本 + 居间费
项目利润 = 项目规划预算 - 总成本 + 预估达人返点
项目毛利率 = 项目利润 / 项目规划预算 × 100%
```

**影响范围**:
- 项目详情页面的毛利显示
- 品牌财务汇总报表
- 财务导出报表
- 项目统计数据

## 技术实现

### 1. 数据库迁移
```sql
ALTER TABLE "projects" ADD COLUMN "intermediaryCost" DECIMAL(15,2) NOT NULL DEFAULT 0;
```

### 2. TypeScript类型定义
```typescript
export interface ProjectCost {
  influencerCost: number;      // 达人成本
  adCost: number;             // 投流成本
  otherCost: number;          // 其他成本
  intermediaryCost: number;   // 居间费
  estimatedInfluencerRebate: number; // 预估达人返点
}
```

### 3. API Schema更新
```javascript
cost: {
  type: 'object',
  required: ['influencerCost', 'adCost', 'otherCost', 'estimatedInfluencerRebate'],
  properties: {
    influencerCost: { type: 'number', minimum: 0, description: '达人成本' },
    adCost: { type: 'number', minimum: 0, description: '投流成本' },
    otherCost: { type: 'number', minimum: 0, description: '其他成本' },
    intermediaryCost: { type: 'number', minimum: 0, default: 0, description: '居间费' },
    estimatedInfluencerRebate: { type: 'number', minimum: 0, description: '预估达人返点' }
  }
}
```

### 4. 数据验证
```typescript
cost: z.object({
  influencerCost: z.number().min(0, '达人成本不能为负数'),
  adCost: z.number().min(0, '投流成本不能为负数'),
  otherCost: z.number().min(0, '其他成本不能为负数'),
  intermediaryCost: z.number().min(0, '居间费不能为负数').default(0),
  estimatedInfluencerRebate: z.number().min(0, '预估达人返点不能为负数')
})
```

## 影响的文件

### 核心文件
1. `prisma/schema.prisma` - 数据库模型定义
2. `src/types/project.ts` - TypeScript类型定义
3. `src/controllers/project.ts` - 项目控制器和验证
4. `src/routes/project.ts` - API路由定义
5. `src/services/database.ts` - 数据库服务
6. `src/services/project.ts` - 项目业务逻辑
7. `src/services/financialExport.ts` - 财务导出服务

### 文档文件
1. `docs/api-types.ts` - API类型文档
2. `docs/api-documentation.md` - API接口文档

### 测试文件
1. `src/tests/notification.test.ts` - 通知测试
2. `src/controllers/projectImport.ts` - 项目导入功能

## 向后兼容性

- **数据库**: 现有项目的居间费默认为0，不影响历史数据
- **API**: 创建项目时如果不提供居间费字段，会自动设置为0
- **计算**: 现有项目的毛利计算会自动包含居间费（值为0），结果保持不变

## 使用示例

### 创建项目（包含居间费）
```json
{
  "documentType": "project_initiation",
  "brandId": "brand-001",
  "projectName": "测试项目",
  "cost": {
    "influencerCost": 350000,
    "adCost": 280000,
    "otherCost": 80000,
    "intermediaryCost": 15000,
    "estimatedInfluencerRebate": 20000
  }
}
```

### 更新项目居间费
```json
{
  "id": "project-001",
  "cost": {
    "intermediaryCost": 25000
  }
}
```

## 测试验证

已通过以下测试验证功能正确性：
1. ✅ 数据库迁移成功
2. ✅ TypeScript编译通过
3. ✅ 毛利计算包含居间费
4. ✅ API接口支持居间费字段
5. ✅ 数据验证防止负数
6. ✅ 默认值机制正常工作

## 部署注意事项

1. **数据库迁移**: 部署时会自动执行数据库迁移，为所有现有项目添加居间费字段（默认值为0）
2. **API兼容性**: 现有的API调用不会受到影响，居间费字段为可选
3. **前端更新**: 前端需要相应更新以支持居间费字段的输入和显示

## 总结

居间费功能的添加使项目成本核算更加完整和准确，有助于：
- 提高财务数据的准确性
- 更真实地反映项目盈利情况
- 为决策提供更可靠的数据支持
- 满足财务合规要求

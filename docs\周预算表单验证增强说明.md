# 周预算表单验证增强说明

## 修改概述

根据用户需求，为周预算相关的表单提交添加了全面的验证逻辑，确保数据的完整性和业务规则的正确性。

## 验证增强内容

### 1. 创建周预算验证 (`createWeeklyBudgetSchema`)

#### 基础字段验证
- **预算标题**: 不能为空，最大200字符
- **开始/结束日期**: 必须是有效的日期格式
- **服务类型**: 必须是有效的枚举值
- **服务内容**: 不能为空
- **合同金额**: 必须大于0（从0改为0.01）
- **税率**: 必须是有效的枚举值
- **已付金额**: 不能为负数，默认为0

#### 业务逻辑验证
```typescript
.refine((data) => {
  // 验证结束日期不能早于开始日期
  const startDate = new Date(data.weekStartDate);
  const endDate = new Date(data.weekEndDate);
  return endDate >= startDate;
}, {
  message: '结束日期不能早于开始日期',
  path: ['weekEndDate']
}).refine((data) => {
  // 验证已付金额不能超过合同金额
  if (data.paidAmount !== undefined) {
    return data.paidAmount <= data.contractAmount;
  }
  return true;
}, {
  message: '已付金额不能超过合同金额',
  path: ['paidAmount']
})
```

### 2. 更新周预算验证 (`updateWeeklyBudgetSchema`)

#### 增强的验证逻辑
- **合同金额**: 添加了错误消息
- **已付金额**: 添加了错误消息
- **跨字段验证**: 已付金额不能超过合同金额

#### 控制器层额外验证
```typescript
// 获取当前周预算信息进行额外验证
const currentBudget = await this.projectService.getWeeklyBudget(id);

// 如果更新已付金额，需要验证不能超过合同金额
if (budgetData.paidAmount !== undefined) {
  const contractAmount = budgetData.contractAmount ?? currentBudget.contractAmount;
  if (budgetData.paidAmount > contractAmount) {
    return reply.status(400).send({
      success: false,
      message: `已付金额(${budgetData.paidAmount})不能超过合同金额(${contractAmount})`
    });
  }
}

// 如果更新合同金额，需要验证不能小于已付金额
if (budgetData.contractAmount !== undefined) {
  const paidAmount = budgetData.paidAmount ?? currentBudget.paidAmount;
  if (budgetData.contractAmount < paidAmount) {
    return reply.status(400).send({
      success: false,
      message: `合同金额(${budgetData.contractAmount})不能小于已付金额(${paidAmount})`
    });
  }
}
```

### 3. 批量创建周预算验证 (`batchCreateWeeklyBudgetSchema`)

#### 新增验证模式
```typescript
const batchCreateWeeklyBudgetSchema = z.object({
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的开始日期格式'),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), '无效的结束日期格式'),
  serviceType: z.nativeEnum(ServiceType, { errorMap: () => ({ message: '无效的服务类型' }) }),
  defaultContractAmount: z.number().min(0.01, '默认合同金额必须大于0'),
  defaultTaxRate: z.nativeEnum(TaxRate, { errorMap: () => ({ message: '无效的税率' }) }),
}).refine((data) => {
  // 验证结束日期不能早于开始日期
  const startDate = new Date(data.startDate);
  const endDate = new Date(data.endDate);
  return endDate >= startDate;
}, {
  message: '结束日期不能早于开始日期',
  path: ['endDate']
});
```

#### 控制器层验证
- **项目ID验证**: 不能为空
- **项目存在性验证**: 验证项目是否存在
- **完整的错误处理**: 包括Zod验证错误和业务错误

### 4. CSV批量上传验证

#### 文件验证
```typescript
// 验证文件类型
const allowedMimeTypes = ['text/csv', 'application/csv', 'text/plain'];
const allowedExtensions = ['.csv'];

// 验证文件大小（限制为10MB）
const maxFileSize = 10 * 1024 * 1024;

// 验证文件内容不为空
if (!fileContent.trim()) {
  return reply.status(400).send({
    success: false,
    message: 'CSV文件内容为空'
  });
}
```

#### CSV数据验证
```typescript
// 验证CSV表头（至少需要包含必要的列）
const requiredColumns = ['项目名称', '供应商名称', '服务类型', '服务内容', '合同金额', '税率'];
const headers = csvData[0] || [];
const missingColumns = requiredColumns.filter(col => !headers.includes(col));

if (missingColumns.length > 0) {
  return reply.status(400).send({
    success: false,
    message: `CSV文件缺少必要的列: ${missingColumns.join(', ')}`
  });
}
```

### 5. 付款审批验证增强

#### 支付金额验证
```typescript
// 验证付款金额不能超过剩余未付金额
const remainingAmount = weeklyBudget.contractAmount - weeklyBudget.paidAmount;
if (approvalData.totalAmount > remainingAmount) {
  return reply.status(400).send({
    success: false,
    message: `付款金额(${approvalData.totalAmount})不能超过剩余未付金额(${remainingAmount})`,
    code: 'PAYMENT_AMOUNT_EXCEEDS_REMAINING'
  });
}

// 验证付款金额不能超过合同总金额
if (approvalData.totalAmount > weeklyBudget.contractAmount) {
  return reply.status(400).send({
    success: false,
    message: `付款金额(${approvalData.totalAmount})不能超过合同总金额(${weeklyBudget.contractAmount})`,
    code: 'PAYMENT_AMOUNT_EXCEEDS_CONTRACT'
  });
}
```

#### 日期验证
```typescript
// 验证期望付款日期格式
const expectedPaymentDate = new Date(approvalData.expectedPaymentDate);
if (isNaN(expectedPaymentDate.getTime())) {
  return reply.status(400).send({
    success: false,
    message: '期望付款时间格式不正确',
    code: 'INVALID_PAYMENT_DATE'
  });
}

// 验证期望付款日期不能是过去的日期
const today = new Date();
today.setHours(0, 0, 0, 0);
expectedPaymentDate.setHours(0, 0, 0, 0);

if (expectedPaymentDate < today) {
  return reply.status(400).send({
    success: false,
    message: '期望付款时间不能早于今天',
    code: 'PAYMENT_DATE_IN_PAST'
  });
}
```

## 错误处理改进

### 统一的错误响应格式
```typescript
if (error instanceof z.ZodError) {
  return reply.status(400).send({
    success: false,
    message: '参数验证失败',
    errors: error.errors
  });
}
```

### 业务错误代码
- `WEEKLY_BUDGET_NOT_FOUND`: 周预算不存在
- `APPROVAL_ALREADY_EXISTS`: 审批已存在
- `PAYMENT_AMOUNT_EXCEEDS_REMAINING`: 付款金额超过剩余金额
- `PAYMENT_AMOUNT_EXCEEDS_CONTRACT`: 付款金额超过合同金额
- `INVALID_PAYMENT_DATE`: 无效的付款日期
- `PAYMENT_DATE_IN_PAST`: 付款日期在过去

## 修改文件

- `src/controllers/weeklyBudget.ts` - 主要的验证逻辑增强
- `src/types/project.ts` - 修复UpdateProjectRequest类型定义
- `src/services/project.ts` - 修复类型兼容性问题

## 类型修复

### UpdateProjectRequest接口增强
```typescript
export interface UpdateProjectRequest  {
  // ... 原有字段

  // 财务回款信息
  expectedPaymentMonth?: string;    // 预计回款月份 (YYYY-MM格式，如2024-03)
  paymentTermDays?: number;         // 账期天数 (如180表示T+180)

  // 附件信息
  attachmentIds?: string[];         // 附件ID列表
}
```

### 项目更新逻辑修复
```typescript
// 正确合并budget和cost字段
const updatedProject: Project = {
  ...project,
  ...request,
  // 正确合并budget字段
  budget: request.budget ? { ...project.budget, ...request.budget } : project.budget,
  // 正确合并cost字段
  cost: request.cost ? { ...project.cost, ...request.cost } : project.cost,
  updatedAt: TimezoneUtils.now(),
  updatedBy
};
```

## 验证效果

- ✅ 防止无效数据提交
- ✅ 确保业务规则正确性
- ✅ 提供清晰的错误信息
- ✅ 支持前端表单验证
- ✅ 保护数据完整性
- ✅ TypeScript编译通过
- ✅ 类型安全保障
